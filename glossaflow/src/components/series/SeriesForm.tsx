'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, BookOpen, Loader2, Save, X, CheckCircle, AlertTriangle, Search, FileText, Sparkles, Zap } from 'lucide-react';
import { useCreateSeriesMutation, useUpdateSeriesMutation, useGetSeriesQuery } from '@/lib/api/series';
import { useGetTerminologyForProjectQuery } from '@/lib/api/terminology';
import { useGetTranslationSuggestionMutation } from '@/lib/api/ai-translation';
import { useGetProjectQuery } from '@/store/api/projectsApi';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getAILanguageName } from '@/lib/utils/language-mapping';
import type { Series } from '@/lib/api/series';

interface SeriesFormProps {
  projectId: string;
  series?: Series; // If provided, we're editing; if not, we're creating
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function SeriesForm({ projectId, series, onSuccess, onCancel }: SeriesFormProps) {
  const router = useRouter();
  const isEditing = !!series;

  const [formData, setFormData] = useState({
    chapter_number: '',
    title: '',
    description: '',
  });

  const [createSeries, { isLoading: isCreating }] = useCreateSeriesMutation();
  const [updateSeries, { isLoading: isUpdating }] = useUpdateSeriesMutation();
  const [getTranslationSuggestion] = useGetTranslationSuggestionMutation();
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [terminologyOpen, setTerminologyOpen] = useState(false);
  const [terminologySearch, setTerminologySearch] = useState('');
  const [isTranslating, setIsTranslating] = useState(false);
  const [detectedTerms, setDetectedTerms] = useState<string[]>([]);

  // Translation comparison state
  const [originalDescription, setOriginalDescription] = useState('');
  const [translatedDescription, setTranslatedDescription] = useState('');
  const [showComparison, setShowComparison] = useState(false);

  const isLoading = isCreating || isUpdating;

  // Fetch project data
  const { data: projectData } = useGetProjectQuery(projectId);

  // Fetch existing series to determine next series number (only for new series)
  const { data: existingSeries } = useGetSeriesQuery({
    projectId,
    limit: 1000
  }, {
    skip: isEditing
  });

  // Fetch project terminology
  const { data: terminologyData, isLoading: terminologyLoading } = useGetTerminologyForProjectQuery({
    projectId
  });

  // Initialize form data
  useEffect(() => {
    if (isEditing && series) {
      // Editing existing series
      const newFormData = {
        chapter_number: series.chapter_number?.toString() || '',
        title: series.title || '',
        description: series.description || '',
      };
      setFormData(newFormData);

      // Detect terminology in existing content
      detectTerminology(newFormData.description);
    } else if (!isEditing && existingSeries?.data?.items && formData.chapter_number === '') {
      // Creating new series - auto-increment number
      const existingNumbers = existingSeries.data.items.map(s => s.chapter_number);
      const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
      setFormData(prev => ({
        ...prev,
        chapter_number: nextNumber.toString(),
      }));
    }
  }, [series, existingSeries, isEditing, formData.chapter_number]);

  // Detect terminology when terminology data loads
  useEffect(() => {
    if (terminologyData?.data && (formData.title || formData.description)) {
      detectTerminology(formData.description);
    }
  }, [terminologyData, formData.title, formData.description]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Detect terminology in title field
    if (name === 'title') {
      detectTerminology(value);
    }
  };

  const handleDescriptionChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      description: value
    }));

    // Detect terminology in the description
    detectTerminology(value);
  };

  // Detect terminology in text
  const detectTerminology = (text: string) => {
    if (!terminologyData?.data) return;

    // Combine title and description for terminology detection
    const combinedText = `${formData.title} ${text || formData.description}`.toLowerCase();

    const terms = terminologyData.data
      .filter(term =>
        combinedText.includes(term.source_term.toLowerCase()) ||
        combinedText.includes(term.target_term.toLowerCase())
      )
      .map(term => term.source_term);

    setDetectedTerms([...new Set(terms)]);
  };

  // Helper function to strip HTML tags and get plain text
  const stripHtmlTags = (html: string): string => {
    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Handle AI translation
  const handleAITranslation = async () => {
    if (!formData.description || !projectData?.project) return;

    // Strip HTML tags to get plain text for translation
    const plainText = stripHtmlTags(formData.description);

    if (!plainText.trim()) {
      setMessage({
        type: 'error',
        text: 'Please enter some text to translate.'
      });
      setTimeout(() => setMessage(null), 3000);
      return;
    }

    // Store original text for comparison
    setOriginalDescription(formData.description);

    setIsTranslating(true);
    try {
      // Get language codes - handle both camelCase and snake_case property names
      const project = projectData.project as any;
      const sourceLanguage = getAILanguageName(project.sourceLanguage || project.source_language || 'English');
      const targetLanguages = project.targetLanguages || project.target_languages || [];
      const rawTargetLanguage = targetLanguages.length > 0 ? targetLanguages[0] : 'Japanese';
      const targetLanguage = getAILanguageName(rawTargetLanguage);

      console.log('AI Translation Request:', {
        sourceText: plainText.substring(0, 100) + '...',
        sourceLanguage,
        targetLanguage,
        rawTargetLanguage,
        targetLanguages,
        projectId,
        projectData: projectData.project,
        projectKeys: Object.keys(projectData.project),
        // Debug both property name formats
        snake_case: {
          source_language: project.source_language,
          target_languages: project.target_languages
        },
        camelCase: {
          sourceLanguage: project.sourceLanguage,
          targetLanguages: project.targetLanguages
        }
      });

      const result = await getTranslationSuggestion({
        sourceText: plainText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        context: `Series description for: ${formData.title}`,
        domain: 'literature',
        projectId: projectId,
      }).unwrap();

      if (result.success && result.data.targetText) {
        // Store translated text and show comparison
        setTranslatedDescription(result.data.targetText);
        setShowComparison(true);

        setMessage({
          type: 'success',
          text: 'AI translation completed! Choose which version to use.'
        });

        // Clear message after 5 seconds (longer for comparison)
        setTimeout(() => setMessage(null), 5000);
      }
    } catch (error) {
      console.error('AI translation failed:', error);
      setMessage({
        type: 'error',
        text: 'AI translation failed. Please try again.'
      });
      setTimeout(() => setMessage(null), 3000);
    } finally {
      setIsTranslating(false);
    }
  };

  // Handle choosing original or translated version
  const handleUseOriginal = () => {
    setShowComparison(false);
    setMessage({
      type: 'success',
      text: 'Using original text.'
    });
    setTimeout(() => setMessage(null), 3000);
  };

  const handleUseTranslated = () => {
    setFormData(prev => ({
      ...prev,
      description: translatedDescription
    }));
    setShowComparison(false);
    setMessage({
      type: 'success',
      text: 'Using translated text.'
    });
    setTimeout(() => setMessage(null), 3000);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.chapter_number || !formData.title) {
      setMessage({
        type: 'error',
        text: 'Series number and title are required.'
      });
      return;
    }

    try {
      if (isEditing && series) {
        // Update existing series
        await updateSeries({
          id: series.id,
          data: {
            chapter_number: parseInt(formData.chapter_number),
            title: formData.title,
            description: formData.description || undefined,
          }
        }).unwrap();

        setMessage({
          type: 'success',
          text: 'Series updated successfully!'
        });

        if (onSuccess) {
          onSuccess();
        }
      } else {
        // Create new series
        const result = await createSeries({
          project_id: projectId,
          chapter_number: parseInt(formData.chapter_number),
          title: formData.title,
          description: formData.description || undefined,
        }).unwrap();

        setMessage({
          type: 'success',
          text: 'Series created successfully!'
        });

        // Navigate to the new series detail page
        setTimeout(() => {
          router.push(`/dashboard/projects/${projectId}/series/${result.data.id}`);
        }, 1500);
      }

      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    } catch (error: any) {
      console.error('Error saving series:', error);
      setMessage({
        type: 'error',
        text: error?.data?.error || `Failed to ${isEditing ? 'update' : 'create'} series. Please try again.`
      });
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push(`/dashboard/projects/${projectId}`);
    }
  };

  // Filter terminology based on search
  const filteredTerminology = terminologyData?.data?.filter(term =>
    term.source_term.toLowerCase().includes(terminologySearch.toLowerCase()) ||
    term.target_term.toLowerCase().includes(terminologySearch.toLowerCase()) ||
    term.category.toLowerCase().includes(terminologySearch.toLowerCase())
  ) || [];

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/dashboard/projects/${projectId}`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {isEditing ? `Edit Series: ${series?.title}` : 'Create New Series'}
            </h1>
            <p className="text-gray-600">
              {isEditing ? 'Update series information' : 'Add a new series to your project'}
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}>
          {message.type === 'error' ? (
            <AlertTriangle className="h-4 w-4 text-red-600" />
          ) : (
            <CheckCircle className="h-4 w-4 text-green-600" />
          )}
          <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
            {message.text}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="mr-2 h-5 w-5" />
                Series Information
              </CardTitle>
              <CardDescription>
                {isEditing ? 'Update the series details below' : 'Enter the details for your new series'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="chapter_number">Series Number</Label>
                    <Input
                      id="chapter_number"
                      name="chapter_number"
                      type="number"
                      min="1"
                      value={formData.chapter_number}
                      onChange={handleInputChange}
                      required
                      disabled={isEditing} // Don't allow changing series number when editing
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Series Title</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="Enter series title..."
                    required
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="description">Description</Label>
                    {!showComparison && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleAITranslation}
                        disabled={isTranslating || !formData.description || !projectData?.project}
                        className="flex items-center space-x-2"
                      >
                        {isTranslating ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Sparkles className="h-4 w-4" />
                        )}
                        <span>{isTranslating ? 'Translating...' : 'AI Translate'}</span>
                      </Button>
                    )}
                  </div>

                  {/* Comparison View */}
                  {showComparison ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {/* Original Text */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium text-blue-700">Original Text</Label>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleUseOriginal}
                              className="text-blue-700 border-blue-300 hover:bg-blue-50"
                            >
                              Use Original
                            </Button>
                          </div>
                          <div className="p-3 border border-blue-200 rounded-lg bg-blue-50 min-h-[120px] max-h-[300px] overflow-y-auto">
                            <div
                              className="text-sm prose prose-sm max-w-none"
                              dangerouslySetInnerHTML={{ __html: originalDescription }}
                            />
                          </div>
                        </div>

                        {/* Translated Text */}
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium text-green-700">Translated Text</Label>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleUseTranslated}
                              className="text-green-700 border-green-300 hover:bg-green-50"
                            >
                              Use Translation
                            </Button>
                          </div>
                          <div className="p-3 border border-green-200 rounded-lg bg-green-50 min-h-[120px] max-h-[300px] overflow-y-auto">
                            <div className="text-sm prose prose-sm max-w-none">
                              {translatedDescription}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Comparison Actions */}
                      <div className="flex items-center justify-center space-x-4 p-4 bg-gray-50 rounded-lg">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setShowComparison(false)}
                          className="flex items-center space-x-2"
                        >
                          <span>Cancel Comparison</span>
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleAITranslation}
                          disabled={isTranslating}
                          className="flex items-center space-x-2"
                        >
                          {isTranslating ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Sparkles className="h-4 w-4" />
                          )}
                          <span>Translate Again</span>
                        </Button>
                      </div>
                    </div>
                  ) : (
                    /* Normal Editor View */
                    <RichTextEditor
                      value={formData.description}
                      onChange={handleDescriptionChange}
                      placeholder="Enter series description..."
                    />
                  )}

                  {/* Terminology Detection */}
                  {detectedTerms.length > 0 && !showComparison && (
                    <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center space-x-2 mb-2">
                        <Zap className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">
                          Detected Terminology ({detectedTerms.length})
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {detectedTerms.map((term, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {term}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-xs text-blue-600 mt-2">
                        These terms are in your project terminology database for consistent translation.
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="mr-2 h-4 w-4" />
                    )}
                    {isEditing ? 'Update Series' : 'Create Series'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Terminology Reference Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader
              className="cursor-pointer hover:bg-gray-50"
              onClick={() => setTerminologyOpen(!terminologyOpen)}
            >
              <CardTitle className="flex items-center justify-between text-base">
                <div className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Terminology Reference
                </div>
                <Badge variant="secondary">
                  {terminologyData?.data?.length || 0} terms
                </Badge>
              </CardTitle>
              <CardDescription>
                Click to {terminologyOpen ? 'hide' : 'view'} project terminology for consistency
              </CardDescription>
            </CardHeader>
            {terminologyOpen && (
              <CardContent className="pt-0">
                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search terminology..."
                      value={terminologySearch}
                      onChange={(e) => setTerminologySearch(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <div className="max-h-96 overflow-y-auto space-y-2">
                    {terminologyLoading ? (
                      <div className="flex items-center justify-center py-4">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </div>
                    ) : filteredTerminology.length > 0 ? (
                      filteredTerminology.map((term) => (
                        <div key={term.id} className="p-3 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="font-medium text-sm">{term.source_term}</p>
                              <p className="text-sm text-gray-600">{term.target_term}</p>
                              {term.context && (
                                <p className="text-xs text-gray-500 mt-1">{term.context}</p>
                              )}
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {term.category}
                            </Badge>
                          </div>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500 text-center py-4">
                        {terminologySearch ? 'No matching terms found' : 'No terminology defined yet'}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
