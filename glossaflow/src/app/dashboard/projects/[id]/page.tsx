'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
// import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'; // Temporarily disabled for team features
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Play,
  Settings,
  Users,
  FileText,
  MessageSquare,
  BarChart3,
  Download,
  Upload,
  Edit,
  BookOpen,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Lightbulb,
  RefreshCw,
  CreditCard
} from 'lucide-react';
import { useGetProjectQuery } from '@/store/api/projectsApi';
import { useGetTerminologyStatsQuery } from '@/store/api/terminologyApi';
import { useGetSeriesQuery } from '@/lib/api/series';
import { useGetCreditsQuery } from '@/lib/api/credits';
import { useGetDashboardActivityQuery } from '@/lib/api/dashboard';
import type { Series } from '@/lib/api/series';

// Extended project interface to handle API response format
interface ProjectWithCamelCase {
  id: string;
  name: string;
  description: string | null;
  status: string;
  priority: string;
  sourceLanguage: string;
  targetLanguages: string[];
  documentType: string;
  createdAt: string;
  updatedAt: string;
  dueDate: string | null;
  budget: number | null;
  spent: number;
  createdBy: string;
  organizationId: string | null;
  totalSegments: number;
  completedSegments: number;
  reviewedSegments: number;
  approvedSegments: number;
  teamMembers: any[];
  files: any[];
}

// Status and priority color mappings

const statusColors = {
  draft: 'bg-gray-100 text-gray-800',
  active: 'bg-blue-100 text-blue-800',
  paused: 'bg-yellow-100 text-yellow-800',
  completed: 'bg-green-100 text-green-800',
  archived: 'bg-red-100 text-red-800',
};

const priorityColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800',
};

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id as string;
  const { data: session, status: sessionStatus } = useSession();

  // Skip API calls until session is ready
  const skipQueries = sessionStatus === 'loading' || !session;

  // Fetch project data
  const {
    data: projectData,
    error: projectError,
    isLoading: projectLoading,
    refetch: refetchProject
  } = useGetProjectQuery(projectId, {
    skip: skipQueries
  });

  // Fetch terminology stats
  const {
    data: terminologyStats,
    error: terminologyError,
    isLoading: terminologyLoading
  } = useGetTerminologyStatsQuery(projectId, {
    skip: skipQueries
  });

  // Fetch series data
  const {
    data: seriesData,
    error: seriesError,
    isLoading: seriesLoading,
    refetch: refetchSeries
  } = useGetSeriesQuery({ projectId }, {
    skip: skipQueries
  });

  // Fetch credits data
  const {
    data: creditsData,
    isLoading: creditsLoading
  } = useGetCreditsQuery(undefined, {
    skip: skipQueries
  });

  // Fetch activity data
  const {
    data: activityData,
    error: activityError,
    isLoading: activityLoading,
    refetch: refetchActivity
  } = useGetDashboardActivityQuery({ limit: 10 }, {
    skip: skipQueries
  });

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    // Don't set up auto-refresh if session is not ready
    if (sessionStatus !== 'authenticated' || !session) {
      return;
    }

    const interval = setInterval(() => {
      // Only refetch if not currently loading and session is authenticated
      if (!projectLoading && !activityLoading && sessionStatus === 'authenticated') {
        refetchProject();
        refetchActivity();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [sessionStatus, session, projectLoading, activityLoading, refetchProject, refetchActivity]);

  // Real-time updates for project progress
  useEffect(() => {
    // Don't set up real-time updates if session is not ready
    if (sessionStatus !== 'authenticated' || !session) {
      return;
    }

    // Simulate real-time updates by refetching when project status changes
    if (projectData?.project?.status === 'active') {
      const progressInterval = setInterval(() => {
        if (sessionStatus === 'authenticated') {
          refetchProject();
        }
      }, 60000); // 1 minute for active projects

      return () => clearInterval(progressInterval);
    }
  }, [sessionStatus, session, projectData?.project?.status, refetchProject]);

  // Handle loading state - include session loading
  const isLoading = sessionStatus === 'loading' || projectLoading || terminologyLoading || seriesLoading || creditsLoading;

  // Redirect to login if not authenticated
  if (sessionStatus === 'unauthenticated') {
    router.push('/auth/signin');
    return null;
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header Skeleton */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1">
              <Skeleton className="h-8 w-1/3 mb-2" />
              <Skeleton className="h-4 w-2/3 mb-4" />
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-28" />
            </div>
          </div>

          {/* Terminology Status Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-5 w-5 rounded" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-20 mb-1" />
                      <Skeleton className="h-6 w-12" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Overview Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <Skeleton className="h-12 w-12 rounded-lg" />
                    <div className="ml-4 flex-1">
                      <Skeleton className="h-4 w-24 mb-2" />
                      <Skeleton className="h-8 w-16" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Progress and Details Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-4 w-60" />
              </CardHeader>
              <CardContent className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i}>
                    <div className="flex justify-between mb-2">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <Skeleton className="h-2 w-full" />
                  </div>
                ))}
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i}>
                      <Skeleton className="h-4 w-24 mb-1" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Handle error state
  if (projectError || !projectData?.project) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-500" />
          <h2 className="text-xl font-semibold text-gray-900">Project Not Found</h2>
          <p className="text-gray-600 text-center max-w-md">
            The project you&apos;re looking for doesn&apos;t exist or you don&apos;t have permission to view it.
          </p>
          <div className="flex space-x-2">
            <Button onClick={() => router.push('/dashboard/projects')}>
              Back to Projects
            </Button>
            <Button variant="outline" onClick={() => refetchProject()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const project = projectData.project;
  // const teamMembers = projectData.team || []; // Temporarily disabled for team features
  const documents = projectData.documents || [];
  const progress = projectData.progress;



  // Extract series data
  const series = seriesData?.data?.items || [];
  const seriesStats = seriesData?.data?.statistics || {
    totalChapters: 0,
    completedChapters: 0,
    inProgressChapters: 0,
    pendingChapters: 0
  };

  // Extract credits data
  const credits = creditsData?.data?.credits || { balance: 0 };

  // Extract activity data
  const recentActivity = activityData?.data?.activities || [];

  // Terminology status from API - fix data structure access
  const terminologyStatus = {
    totalTerms: terminologyStats?.totalEntries || 0,
    approvedTerms: terminologyStats?.approvedEntries || 0,
    pendingTerms: terminologyStats?.pendingEntries || 0,
    rejectedTerms: terminologyStats?.rejectedEntries || 0,
    consistencyScore: 0.85, // Default consistency score
    isSetupComplete: (terminologyStats?.approvedEntries || 0) > 0,
    canStartTranslation: (terminologyStats?.approvedEntries || 0) > 0,
    nextStep: (terminologyStats?.approvedEntries || 0) > 0 ? 'ready' : 'setup_terminology'
  };

  // Calculate progress percentages
  const totalSegments = progress?.totalSegments || project.total_segments || 0;
  const completedSegments = progress?.translatedSegments || project.completed_segments || 0;
  const reviewedSegments = progress?.reviewedSegments || project.reviewed_segments || 0;
  const approvedSegments = progress?.approvedSegments || project.approved_segments || 0;

  const progressPercentage = totalSegments > 0 ? (completedSegments / totalSegments) * 100 : 0;
  const reviewProgress = totalSegments > 0 ? (reviewedSegments / totalSegments) * 100 : 0;
  const approvalProgress = totalSegments > 0 ? (approvedSegments / totalSegments) * 100 : 0;

  // Format dates
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  // Handle retry for failed requests
  const handleRetry = () => {
    // Only retry if session is authenticated
    if (sessionStatus === 'authenticated' && session) {
      refetchProject();
      refetchSeries();
      refetchActivity();
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-2xl font-bold text-gray-900">{project.name}</h1>
              <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                {project.status.replace('_', ' ')}
              </Badge>
              <Badge className={priorityColors[project.priority as keyof typeof priorityColors]}>
                {project.priority} priority
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/dashboard/projects/${projectId}/edit`)}
                title="Edit project details"
              >
                <Edit className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-gray-600">{project.description || 'No description provided'}</p>
          </div>
          <div className="flex flex-wrap gap-2 mt-4 sm:mt-0">
            <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/projects/${projectId}/settings`)}
            >
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/projects/${projectId}/terminology`)}
            >
              <BookOpen className="mr-2 h-4 w-4" />
              Terminology ({terminologyStatus.totalTerms})
            </Button>

            {/* Primary action based on project state */}
            {credits.balance === 0 ? (
              <Button onClick={() => router.push('/dashboard/credits')}>
                <CreditCard className="mr-2 h-4 w-4" />
                Buy Credits
              </Button>
            ) : !terminologyStatus.canStartTranslation ? (
              <Button onClick={() => router.push(`/dashboard/projects/${projectId}/setup`)}>
                <Lightbulb className="mr-2 h-4 w-4" />
                Complete Setup
              </Button>
            ) : series.length === 0 ? (
              <Button onClick={() => router.push(`/dashboard/projects/${projectId}/series/new`)}>
                <BookOpen className="mr-2 h-4 w-4" />
                Add Series
              </Button>
            ) : (
              <Button onClick={() => router.push(`/dashboard/projects/${projectId}/translate`)}>
                <Play className="mr-2 h-4 w-4" />
                Continue Translation
              </Button>
            )}

            {/* Secondary actions - Team functionality temporarily disabled */}
            {/* <Button
              variant="outline"
              onClick={() => router.push(`/dashboard/projects/${projectId}/team`)}
            >
              <Users className="mr-2 h-4 w-4" />
              Team ({teamMembers.length})
            </Button> */}

            {documents.length > 0 && (
              <Button
                variant="outline"
                onClick={() => router.push(`/dashboard/projects/${projectId}/files`)}
              >
                <FileText className="mr-2 h-4 w-4" />
                Files ({documents.length})
              </Button>
            )}
          </div>
        </div>

        {/* Credits Status Alert */}
        {credits.balance === 0 && (
          <Alert>
            <CreditCard className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <strong>Credits Required:</strong> You need credits to start translation work. Purchase credits to continue.
                </div>
                <Button
                  size="sm"
                  onClick={() => router.push('/dashboard/credits')}
                >
                  Buy Credits
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Terminology Workflow Status */}
        {!terminologyStatus.canStartTranslation && credits.balance > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <strong>Terminology Setup Required:</strong> Complete terminology setup before starting translation work.
                  {terminologyStatus.nextStep === 'setup_terminology' && ' Define key terms for consistent translation.'}
                  {terminologyStatus.nextStep === 'review_pending_terms' && ` ${terminologyStatus.pendingTerms} terms need approval.`}
                </div>
                <Button
                  size="sm"
                  onClick={() => router.push(`/dashboard/projects/${projectId}/setup`)}
                >
                  {terminologyStatus.nextStep === 'setup_terminology' ? 'Start Setup' : 'Review Terms'}
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {terminologyStatus.canStartTranslation && credits.balance > 0 && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <strong>Ready for Translation:</strong> Terminology setup is complete and you have {credits.balance} credits available.
                </div>
                <Button
                  size="sm"
                  onClick={() => router.push(`/dashboard/projects/${projectId}/translate`)}
                >
                  <Play className="mr-2 h-4 w-4" />
                  Start Translation
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Terminology Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              {terminologyLoading ? (
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-5 w-5 rounded" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-20 mb-1" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              ) : terminologyError ? (
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Terms</p>
                    <p className="text-sm text-red-500">Error loading</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Terms</p>
                    <p className="text-2xl font-bold text-gray-900">{terminologyStatus.totalTerms}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              {terminologyLoading ? (
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-5 w-5 rounded" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-20 mb-1" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              ) : terminologyError ? (
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Approved</p>
                    <p className="text-sm text-red-500">Error loading</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Approved</p>
                    <p className="text-2xl font-bold text-gray-900">{terminologyStatus.approvedTerms}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              {terminologyLoading ? (
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-5 w-5 rounded" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-20 mb-1" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              ) : terminologyError ? (
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-sm text-red-500">Error loading</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Clock className="h-5 w-5 text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-2xl font-bold text-gray-900">{terminologyStatus.pendingTerms}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              {terminologyLoading ? (
                <div className="flex items-center space-x-2">
                  <Skeleton className="h-5 w-5 rounded" />
                  <div className="flex-1">
                    <Skeleton className="h-4 w-20 mb-1" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                </div>
              ) : terminologyError ? (
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Consistency</p>
                    <p className="text-sm text-red-500">Error loading</p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Consistency</p>
                    <p className="text-2xl font-bold text-gray-900">{Math.round(terminologyStatus.consistencyScore * 100)}%</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Segments</p>
                  <p className="text-2xl font-bold text-gray-900">{totalSegments.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Progress</p>
                  <p className="text-2xl font-bold text-gray-900">{Math.round(progressPercentage)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <BookOpen className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Series</p>
                  <p className="text-2xl font-bold text-gray-900">{seriesStats.totalChapters}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Statistics Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Translation Progress</CardTitle>
              <CardDescription>Track completion across different stages</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Translation</span>
                  <span>{completedSegments.toLocaleString()}/{totalSegments.toLocaleString()} segments</span>
                </div>
                <Progress value={progressPercentage} />
                <p className="text-xs text-gray-500 mt-1">{Math.round(progressPercentage)}% complete</p>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Review</span>
                  <span>{reviewedSegments.toLocaleString()}/{totalSegments.toLocaleString()} segments</span>
                </div>
                <Progress value={reviewProgress} />
                <p className="text-xs text-gray-500 mt-1">{Math.round(reviewProgress)}% reviewed</p>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Approval</span>
                  <span>{approvedSegments.toLocaleString()}/{totalSegments.toLocaleString()} segments</span>
                </div>
                <Progress value={approvalProgress} />
                <p className="text-xs text-gray-500 mt-1">{Math.round(approvalProgress)}% approved</p>
              </div>

              {/* Quality Score */}
              <div className="pt-2 border-t">
                <div className="flex justify-between text-sm mb-2">
                  <span>Quality Score</span>
                  <span>{Math.round(terminologyStatus.consistencyScore * 100)}%</span>
                </div>
                <Progress value={terminologyStatus.consistencyScore * 100} />
                <p className="text-xs text-gray-500 mt-1">Based on terminology consistency</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Metrics</CardTitle>
              <CardDescription>Key performance indicators</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600">Series</p>
                  <p className="text-2xl font-bold text-gray-900">{seriesStats.totalChapters}</p>
                  <p className="text-xs text-gray-500">{seriesStats.completedChapters} completed</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Documents</p>
                  <p className="text-2xl font-bold text-gray-900">{documents.length}</p>
                  <p className="text-xs text-gray-500">Source files</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Credits</p>
                  <p className="text-2xl font-bold text-gray-900">{credits.balance || 0}</p>
                  <p className="text-xs text-gray-500">Available balance</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Status</p>
                  <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                    {project.status.replace('_', ' ')}
                  </Badge>
                  <p className="text-xs text-gray-500 mt-1">Current state</p>
                </div>
              </div>

              {/* Estimated completion */}
              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Estimated Completion</p>
                    <p className="text-sm text-gray-500">
                      {progressPercentage > 0
                        ? `${Math.ceil((100 - progressPercentage) / (progressPercentage / 30))} days remaining`
                        : 'Not started'
                      }
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-600">Priority</p>
                    <Badge className={priorityColors[project.priority as keyof typeof priorityColors]}>
                      {project.priority}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
              <CardDescription>Configuration and settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Source Language</p>
                <p className="text-sm">{(project as unknown as ProjectWithCamelCase).sourceLanguage}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Target Languages</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {(project as unknown as ProjectWithCamelCase).targetLanguages?.map((lang: string) => (
                    <Badge key={lang} variant="outline" className="text-xs">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Document Type</p>
                <p className="text-sm capitalize">{project.document_type}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Created</p>
                <p className="text-sm">{formatDate(project.created_at)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Last Updated</p>
                <p className="text-sm">{formatDate(project.updated_at)}</p>
              </div>
              {project.deadline && (
                <div>
                  <p className="text-sm font-medium text-gray-600">Deadline</p>
                  <p className="text-sm">{formatDate(project.deadline)}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Series Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Series</CardTitle>
              <CardDescription>Manage translation progress by series</CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => refetchSeries()}
                disabled={seriesLoading}
                title="Refresh series data"
              >
                <RefreshCw className={`h-4 w-4 ${seriesLoading ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/dashboard/projects/${projectId}/series/new`)}
              >
                <BookOpen className="mr-2 h-4 w-4" />
                Add Series
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {seriesLoading ? (
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Skeleton className="h-8 w-8 rounded" />
                        <div className="flex-1">
                          <Skeleton className="h-4 w-32 mb-1" />
                          <Skeleton className="h-3 w-48" />
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-4 w-12" />
                        <Skeleton className="h-6 w-16" />
                        <Skeleton className="h-8 w-8" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : seriesError ? (
                <div className="text-center py-8">
                  <AlertTriangle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Failed to load series</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={refetchSeries}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry
                  </Button>
                </div>
              ) : series.length > 0 ? (
                series.map((chapter: Series) => (
                  <div key={chapter.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-blue-100 rounded">
                        <BookOpen className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Series {chapter.chapter_number}: {chapter.title}</p>
                        <p className="text-xs text-gray-500">
                          {chapter.source_word_count} words •
                          {chapter.status} •
                          {Math.round(chapter.progress_percentage || 0)}% complete
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-right">
                        <p className="text-sm font-medium">{Math.round(chapter.progress_percentage || 0)}%</p>
                        <Progress value={chapter.progress_percentage || 0} className="w-16" />
                      </div>
                      <Badge variant={chapter.status === 'approved' ? 'default' : 'outline'} className="text-xs">
                        {chapter.status}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          // Add some debugging for the series navigation
                          console.log('Navigating to series:', chapter.id, 'for project:', projectId);
                          router.push(`/dashboard/projects/${projectId}/series/${chapter.id}`);
                        }}
                        title={`Edit series: ${chapter.title}`}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No series created yet</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => router.push(`/dashboard/projects/${projectId}/series/new`)}
                  >
                    Create First Series
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Files Section - Team functionality temporarily disabled */}
        <div className="grid grid-cols-1 gap-6">
          {/* Team Members Section - Temporarily Disabled */}
          {/* <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Team Members</CardTitle>
                <CardDescription>Project contributors and their roles</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/dashboard/projects/${projectId}/team`)}
              >
                <Users className="mr-2 h-4 w-4" />
                Manage Team
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamMembers.length > 0 ? (
                  teamMembers.map((member) => (
                    <div key={member.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={member.user?.avatar_url || undefined} />
                          <AvatarFallback>
                            {member.user?.name?.split(' ').map((n: string) => n[0]).join('') || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="text-sm font-medium">{member.user?.name || 'Unknown User'}</p>
                          <p className="text-xs text-gray-500">{member.role}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {member.completedSegments || 0}
                        </p>
                        <p className="text-xs text-gray-500">
                          {member.role.includes('reviewer') ? 'reviewed' : 'completed'}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500 text-center py-4">No team members assigned</p>
                )}
              </div>
            </CardContent>
          </Card> */}

          {/* Project Files */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Project Files</CardTitle>
                <CardDescription>Source documents and assets</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/dashboard/projects/${projectId}/files/upload`)}
              >
                <Upload className="mr-2 h-4 w-4" />
                Upload Files
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {documents.length > 0 ? (
                  documents.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded">
                          <FileText className="h-4 w-4 text-gray-600" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">{file.filename}</p>
                          <p className="text-xs text-gray-500">
                            {file.file_size ? `${Math.round(file.file_size / 1024)} KB` : 'Unknown size'} •
                            {file.segment_count} segments •
                            {formatDate(file.created_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {file.mime_type || 'Unknown'}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(file.file_path, '_blank')}
                          title="Download file"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500 text-center py-4">No documents uploaded</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and changes to the project</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activityLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-3">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="flex-1">
                        <Skeleton className="h-4 w-3/4 mb-1" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : activityError ? (
                <div className="text-center py-8">
                  <AlertTriangle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Failed to load activity</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={refetchActivity}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry
                  </Button>
                </div>
              ) : recentActivity.length > 0 ? (
                recentActivity.map((activity) => {
                  const getActivityIcon = (type: string) => {
                    switch (type) {
                      case 'translation': return FileText;
                      case 'comment': return MessageSquare;
                      case 'review': return CheckCircle;
                      case 'assignment': return Users;
                      case 'upload': return Upload;
                      case 'edit': return Edit;
                      default: return MessageSquare;
                    }
                  };

                  const Icon = getActivityIcon(activity.type);

                  return (
                    <div key={activity.id} className="flex items-center space-x-3">
                      <div className="p-1 bg-blue-100 rounded-full">
                        <Icon className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm">
                          <span className="font-medium">{activity.user?.name || 'Unknown User'}</span>{' '}
                          {activity.action}{' '}
                          <span className="font-medium">{activity.title}</span>
                        </p>
                        <p className="text-xs text-gray-500">{formatDate(activity.timestamp)}</p>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-8">
                  <MessageSquare className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No recent activity</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={handleRetry}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
