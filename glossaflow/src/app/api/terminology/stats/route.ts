import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get project ID from query parameters
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    // Build query for terminology entries
    let query = supabase
      .from('terminology_entries')
      .select('approval_status, target_language, category, project_id');

    // Filter by project ID if provided
    if (projectId) {
      query = query.eq('project_id', projectId);
    } else {
      // If no project ID, filter by user's accessible projects
      // For now, we'll return empty stats to avoid confusion
      return NextResponse.json({
        success: true,
        data: {
          total: 0,
          approved: 0,
          pending: 0,
          rejected: 0,
          byLanguage: {},
          byCategory: {},
        },
      });
    }

    const { data: terminology, error: terminologyError } = await query;

    if (terminologyError) {
      console.error('Database error:', terminologyError);
      return NextResponse.json(
        { error: 'Failed to fetch terminology stats', success: false },
        { status: 500 }
      );
    }

    // Calculate statistics
    const total = terminology?.length || 0;
    const approved = terminology?.filter(t => t.approval_status === 'approved').length || 0;
    const pending = terminology?.filter(t => t.approval_status === 'pending').length || 0;
    const rejected = terminology?.filter(t => t.approval_status === 'rejected').length || 0;

    // Calculate by language
    const byLanguage: Record<string, number> = {};
    terminology?.forEach(t => {
      byLanguage[t.target_language] = (byLanguage[t.target_language] || 0) + 1;
    });

    // Calculate by category
    const byCategory: Record<string, number> = {};
    terminology?.forEach(t => {
      byCategory[t.category] = (byCategory[t.category] || 0) + 1;
    });

    return NextResponse.json({
      success: true,
      data: {
        total,
        approved,
        pending,
        rejected,
        byLanguage,
        byCategory,
      },
    });
  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
