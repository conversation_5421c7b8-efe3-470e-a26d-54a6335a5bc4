import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase/service';

/**
 * Clear all terminology entries for a project
 * DELETE /api/terminology/clear?projectId=<project-id>
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get project ID from query params
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required', success: false },
        { status: 400 }
      );
    }

    const supabase = createServiceClient();

    // Verify user has access to this project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, created_by')
      .eq('id', projectId)
      .single();

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found', success: false },
        { status: 404 }
      );
    }

    // Check if user is the project owner or has permission
    if (project.created_by !== session.user.id) {
      // Check if user is a project member with appropriate permissions
      const { data: membership } = await supabase
        .from('project_members')
        .select('role')
        .eq('project_id', projectId)
        .eq('user_id', session.user.id)
        .single();

      if (!membership || !['project_manager', 'admin'].includes(membership.role)) {
        return NextResponse.json(
          { error: 'Insufficient permissions', success: false },
          { status: 403 }
        );
      }
    }

    // Get count of existing terminology entries
    const { count: existingCount, error: countError } = await supabase
      .from('terminology_entries')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId);

    if (countError) {
      console.error('Error counting terminology entries:', countError);
      return NextResponse.json(
        { error: 'Failed to count terminology entries', success: false },
        { status: 500 }
      );
    }

    if (existingCount === 0) {
      return NextResponse.json({
        success: true,
        message: 'No terminology entries to clear',
        deletedCount: 0,
      });
    }

    // Delete all terminology entries for this project
    const { error: deleteError } = await supabase
      .from('terminology_entries')
      .delete()
      .eq('project_id', projectId);

    if (deleteError) {
      console.error('Error deleting terminology entries:', deleteError);
      return NextResponse.json(
        { error: 'Failed to clear terminology entries', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Successfully cleared ${existingCount} terminology entries`,
      deletedCount: existingCount,
    });

  } catch (error) {
    console.error('Clear terminology error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

/**
 * Clear sample terminology entries across all projects
 * DELETE /api/terminology/clear?type=sample
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { type, projectId } = body;

    if (type !== 'sample') {
      return NextResponse.json(
        { error: 'Invalid clear type', success: false },
        { status: 400 }
      );
    }

    const supabase = createServiceClient();

    // Define common sample terms that get added by migrations/scripts
    const sampleTerms = [
      'dragon', 'hero', 'magic sword', 'ancient castle',
      'User Interface', 'Dashboard', 'API', 'Database',
      'character', 'location', 'weapon', 'spell',
      'protagonist', 'antagonist', 'quest', 'adventure'
    ];

    let query = supabase
      .from('terminology_entries')
      .delete()
      .in('source_term', sampleTerms);

    // If projectId is provided, only clear sample terms from that project
    if (projectId) {
      // Verify user has access to this project
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('id, created_by')
        .eq('id', projectId)
        .single();

      if (projectError || !project) {
        return NextResponse.json(
          { error: 'Project not found', success: false },
          { status: 404 }
        );
      }

      if (project.created_by !== session.user.id) {
        const { data: membership } = await supabase
          .from('project_members')
          .select('role')
          .eq('project_id', projectId)
          .eq('user_id', session.user.id)
          .single();

        if (!membership || !['project_manager', 'admin'].includes(membership.role)) {
          return NextResponse.json(
            { error: 'Insufficient permissions', success: false },
            { status: 403 }
          );
        }
      }

      query = query.eq('project_id', projectId);
    }

    const { data: deletedTerms, error: deleteError } = await query.select();

    if (deleteError) {
      console.error('Error deleting sample terminology:', deleteError);
      return NextResponse.json(
        { error: 'Failed to clear sample terminology', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Successfully cleared ${deletedTerms?.length || 0} sample terminology entries`,
      deletedCount: deletedTerms?.length || 0,
      deletedTerms: deletedTerms?.map(term => ({
        sourceTerm: term.source_term,
        targetTerm: term.target_term,
        category: term.category
      })) || [],
    });

  } catch (error) {
    console.error('Clear sample terminology error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
