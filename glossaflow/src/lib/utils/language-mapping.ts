/**
 * Language mapping utilities for GlossaFlow
 * Maps between language names, codes, and AI-friendly formats
 */

export interface LanguageInfo {
  code: string;
  name: string;
  nativeName: string;
  aiName: string; // Name to use with AI services
}

export const SUPPORTED_LANGUAGES: Record<string, LanguageInfo> = {
  // English
  'en': {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    aiName: 'English'
  },
  'english': {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    aiName: 'English'
  },

  // Japanese
  'ja': {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    aiName: 'Japanese'
  },
  'japanese': {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    aiName: 'Japanese'
  },

  // Thai
  'th': {
    code: 'th',
    name: 'Thai',
    nativeName: 'ไทย',
    aiName: 'Thai'
  },
  'thai': {
    code: 'th',
    name: 'Thai',
    nativeName: 'ไทย',
    aiName: 'Thai'
  },

  // Indonesian
  'id': {
    code: 'id',
    name: 'Indonesian',
    nativeName: 'Bahasa Indonesia',
    aiName: 'Indonesian'
  },
  'indonesian': {
    code: 'id',
    name: 'Indonesian',
    nativeName: 'Bahasa Indonesia',
    aiName: 'Indonesian'
  },

  // Vietnamese
  'vi': {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    aiName: 'Vietnamese'
  },
  'vietnamese': {
    code: 'vi',
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    aiName: 'Vietnamese'
  },

  // Filipino
  'fil': {
    code: 'fil',
    name: 'Filipino',
    nativeName: 'Filipino',
    aiName: 'Filipino'
  },
  'filipino': {
    code: 'fil',
    name: 'Filipino',
    nativeName: 'Filipino',
    aiName: 'Filipino'
  },

  // Spanish
  'es': {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    aiName: 'Spanish'
  },
  'spanish': {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    aiName: 'Spanish'
  },

  // French
  'fr': {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    aiName: 'French'
  },
  'french': {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    aiName: 'French'
  },

  // German
  'de': {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    aiName: 'German'
  },
  'german': {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    aiName: 'German'
  },

  // Chinese (Simplified)
  'zh-cn': {
    code: 'zh-cn',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    aiName: 'Chinese (Simplified)'
  },
  'chinese': {
    code: 'zh-cn',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    aiName: 'Chinese (Simplified)'
  },

  // Korean
  'ko': {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    aiName: 'Korean'
  },
  'korean': {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    aiName: 'Korean'
  },
};

/**
 * Get language info by any identifier (code, name, etc.)
 */
export function getLanguageInfo(identifier: string): LanguageInfo | null {
  if (!identifier) return null;
  
  const key = identifier.toLowerCase().trim();
  return SUPPORTED_LANGUAGES[key] || null;
}

/**
 * Get AI-friendly language name for translation services
 */
export function getAILanguageName(identifier: string): string {
  const info = getLanguageInfo(identifier);
  return info?.aiName || identifier; // Fallback to original if not found
}

/**
 * Get language code from any identifier
 */
export function getLanguageCode(identifier: string): string {
  const info = getLanguageInfo(identifier);
  return info?.code || identifier; // Fallback to original if not found
}

/**
 * Get display name for language
 */
export function getLanguageDisplayName(identifier: string): string {
  const info = getLanguageInfo(identifier);
  return info?.name || identifier; // Fallback to original if not found
}

/**
 * Check if a language is supported
 */
export function isLanguageSupported(identifier: string): boolean {
  return getLanguageInfo(identifier) !== null;
}

/**
 * Get all supported languages as options for dropdowns
 */
export function getSupportedLanguageOptions(): Array<{
  value: string;
  label: string;
  nativeName: string;
}> {
  const uniqueLanguages = new Map<string, LanguageInfo>();
  
  // Get unique languages by code
  Object.values(SUPPORTED_LANGUAGES).forEach(lang => {
    if (!uniqueLanguages.has(lang.code)) {
      uniqueLanguages.set(lang.code, lang);
    }
  });

  return Array.from(uniqueLanguages.values()).map(lang => ({
    value: lang.code,
    label: lang.name,
    nativeName: lang.nativeName,
  }));
}
