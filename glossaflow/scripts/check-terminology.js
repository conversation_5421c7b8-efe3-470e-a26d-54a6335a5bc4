#!/usr/bin/env node

/**
 * Check Terminology Data
 * This script shows what terminology entries exist for a specific project
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTerminology(projectId) {
  console.log(`🔍 Checking terminology for project: ${projectId}`);
  
  try {
    // Get all terminology entries for this project
    const { data: terms, error } = await supabase
      .from('terminology_entries')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching terminology:', error);
      return;
    }

    console.log(`📊 Found ${terms?.length || 0} terminology entries:`);
    
    if (terms && terms.length > 0) {
      console.log('\n📋 Terminology List:');
      terms.forEach((term, index) => {
        console.log(`  ${index + 1}. "${term.source_term}" → "${term.target_term}"`);
        console.log(`     Category: ${term.category}`);
        console.log(`     Language: ${term.target_language}`);
        console.log(`     Status: ${term.approval_status}`);
        console.log(`     Created: ${new Date(term.created_at).toLocaleString()}`);
        console.log(`     ID: ${term.id}`);
        console.log('');
      });
    } else {
      console.log('✅ No terminology entries found - your project has a clean terminology list!');
    }

    // Also check terminology stats
    const { data: stats, error: statsError } = await supabase
      .rpc('get_terminology_stats', { project_id: projectId });

    if (!statsError && stats) {
      console.log('📈 Terminology Statistics:');
      console.log(`  Total: ${stats.total_entries || 0}`);
      console.log(`  Approved: ${stats.approved_entries || 0}`);
      console.log(`  Pending: ${stats.pending_entries || 0}`);
      console.log(`  Rejected: ${stats.rejected_entries || 0}`);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function checkAllTerminology() {
  console.log('🔍 Checking ALL terminology in database');
  
  try {
    const { data: terms, error } = await supabase
      .from('terminology_entries')
      .select('project_id, source_term, target_term, category, approval_status')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching terminology:', error);
      return;
    }

    console.log(`📊 Found ${terms?.length || 0} total terminology entries across all projects:`);
    
    if (terms && terms.length > 0) {
      // Group by project
      const byProject = terms.reduce((acc, term) => {
        if (!acc[term.project_id]) {
          acc[term.project_id] = [];
        }
        acc[term.project_id].push(term);
        return acc;
      }, {});

      Object.entries(byProject).forEach(([projectId, projectTerms]) => {
        console.log(`\n📁 Project ${projectId}: ${projectTerms.length} terms`);
        projectTerms.forEach((term, index) => {
          console.log(`  ${index + 1}. "${term.source_term}" → "${term.target_term}" (${term.category}) [${term.approval_status}]`);
        });
      });
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('📖 Usage:');
    console.log('  Check specific project: node check-terminology.js <project-id>');
    console.log('  Check all projects: node check-terminology.js --all');
    console.log('');
    console.log('📝 Examples:');
    console.log('  node check-terminology.js 55625db9-f1cf-4003-b5c1-1358130ea744');
    console.log('  node check-terminology.js --all');
    return;
  }

  if (args[0] === '--all') {
    await checkAllTerminology();
  } else {
    const projectId = args[0];
    if (!projectId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.error('❌ Invalid project ID format');
      return;
    }
    await checkTerminology(projectId);
  }
}

main().catch(console.error);
