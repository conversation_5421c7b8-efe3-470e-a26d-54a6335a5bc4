#!/usr/bin/env node

/**
 * Clear Sample Terminology Data
 * This script removes sample/demo terminology entries from a specific project
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function clearSampleTerminology(projectId) {
  console.log(`🧹 Clearing sample terminology for project: ${projectId}`);
  
  try {
    // First, let's see what terminology exists
    const { data: existingTerms, error: fetchError } = await supabase
      .from('terminology_entries')
      .select('id, source_term, target_term, category, approval_status')
      .eq('project_id', projectId);

    if (fetchError) {
      console.error('❌ Error fetching terminology:', fetchError);
      return;
    }

    if (!existingTerms || existingTerms.length === 0) {
      console.log('✅ No terminology entries found for this project');
      return;
    }

    console.log(`📋 Found ${existingTerms.length} terminology entries:`);
    existingTerms.forEach((term, index) => {
      console.log(`  ${index + 1}. "${term.source_term}" → "${term.target_term}" (${term.category}) [${term.approval_status}]`);
    });

    // Ask for confirmation
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise((resolve) => {
      rl.question('\n❓ Do you want to delete ALL these terminology entries? (y/N): ', resolve);
    });

    rl.close();

    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Operation cancelled');
      return;
    }

    // Delete all terminology entries for this project
    const { error: deleteError } = await supabase
      .from('terminology_entries')
      .delete()
      .eq('project_id', projectId);

    if (deleteError) {
      console.error('❌ Error deleting terminology:', deleteError);
      return;
    }

    console.log('✅ Successfully cleared all terminology entries!');
    console.log('🎉 Your project now has a clean terminology list');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

async function clearAllSampleData() {
  console.log('🧹 Clearing ALL sample terminology data from database');
  
  try {
    // Sample terms that are commonly added by migrations
    const sampleTerms = [
      'dragon', 'hero', 'magic sword', 'ancient castle',
      'User Interface', 'Dashboard', 'API', 'Database',
      'character', 'location', 'weapon', 'spell'
    ];

    console.log('📋 Looking for sample terms:', sampleTerms.join(', '));

    // Delete sample terms
    const { data: deletedTerms, error: deleteError } = await supabase
      .from('terminology_entries')
      .delete()
      .in('source_term', sampleTerms)
      .select();

    if (deleteError) {
      console.error('❌ Error deleting sample terminology:', deleteError);
      return;
    }

    console.log(`✅ Deleted ${deletedTerms?.length || 0} sample terminology entries`);
    
    if (deletedTerms && deletedTerms.length > 0) {
      console.log('🗑️  Deleted terms:');
      deletedTerms.forEach(term => {
        console.log(`  - "${term.source_term}" → "${term.target_term}"`);
      });
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('📖 Usage:');
    console.log('  Clear specific project: node clear-sample-terminology.js <project-id>');
    console.log('  Clear all sample data: node clear-sample-terminology.js --all');
    console.log('');
    console.log('📝 Examples:');
    console.log('  node clear-sample-terminology.js 55625db9-f1cf-4003-b5c1-1358130ea744');
    console.log('  node clear-sample-terminology.js --all');
    return;
  }

  if (args[0] === '--all') {
    await clearAllSampleData();
  } else {
    const projectId = args[0];
    if (!projectId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.error('❌ Invalid project ID format');
      console.error('Project ID should be a UUID like: 55625db9-f1cf-4003-b5c1-1358130ea744');
      return;
    }
    await clearSampleTerminology(projectId);
  }
}

main().catch(console.error);
